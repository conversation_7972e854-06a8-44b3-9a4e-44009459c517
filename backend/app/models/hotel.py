"""
Hotel model for Hotel Pipeline.
"""

from typing import Dict, Any
from sqlalchemy import Column, String, Text, <PERSON>olean, DateTime, Integer, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .base import Base
from .id_generators import generate_hotel_id


class Hotel(Base):
    """Hotel table for Hotel Pipeline"""

    __tablename__ = "hotel"

    id = Column(String(26), primary_key=True, index=True, default=generate_hotel_id)
    name = Column(String(255), nullable=True, index=True)
    destination_id = Column(
        String(26), ForeignKey("destination.id"), nullable=True, index=True
    )
    category_id = Column(
        String(255), ForeignKey("product_category.id"), nullable=True, index=True
    )

    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(
        DateTime, default=func.now(), onupdate=func.now(), nullable=False
    )

    is_active = Column(Boolean, nullable=True)
    currency = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)
    hotel_name = Column(String(255), nullable=True)
    website = Column(String(255), nullable=True)

    # Relationships
    destination = relationship("Destination", back_populates="hotel")
    category = relationship("ProductCategory", back_populates="hotel")

    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary"""
        return {
            "id": self.id,
            "name": self.name,
            "destination_id": self.destination_id,
            "category_id": self.category_id,
            "is_active": self.is_active,
            "currency": self.currency,
            "description": self.description,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


__all__ = ["Hotel"]
